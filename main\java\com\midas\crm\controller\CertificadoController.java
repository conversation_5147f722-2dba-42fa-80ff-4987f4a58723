package com.midas.crm.controller;

import com.midas.crm.entity.DTO.certificado.CertificadoCreateDTO;
import com.midas.crm.entity.DTO.certificado.CertificadoDTO;
import com.midas.crm.entity.Role;
import com.midas.crm.entity.User;
import com.midas.crm.exception.MidasExceptions;
import com.midas.crm.response.GenericResponse;
import com.midas.crm.service.CertificadoService;
import com.midas.crm.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/certificados")
@Slf4j
@CrossOrigin(origins = "*")
public class CertificadoController {

    @Autowired
    private CertificadoService certificadoService;

    @Autowired
    private UserService userService;

    /**
     * Crear un nuevo certificado (solo administradores)
     */
    @PostMapping
    public ResponseEntity<GenericResponse<CertificadoDTO>> crearCertificado(
            @RequestBody CertificadoCreateDTO certificadoCreateDTO) {
        try {
            // Obtener el usuario autenticado
            User adminUser = obtenerUsuarioAutenticado();
            
            // Verificar que sea administrador
            if (!esAdministrador(adminUser)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(new GenericResponse<>(null, "Solo los administradores pueden crear certificados", false));
            }

            CertificadoDTO certificado = certificadoService.crearCertificado(certificadoCreateDTO, adminUser.getId());
            
            return ResponseEntity.ok(new GenericResponse<>(certificado, "Certificado creado exitosamente", true));
        } catch (MidasExceptions e) {
            log.error("Error al crear certificado: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(new GenericResponse<>(null, e.getMessage(), false));
        } catch (Exception e) {
            log.error("Error inesperado al crear certificado: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(null, "Error interno del servidor", false));
        }
    }

    /**
     * Obtener certificado por ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<GenericResponse<CertificadoDTO>> obtenerCertificado(@PathVariable Long id) {
        try {
            CertificadoDTO certificado = certificadoService.obtenerCertificadoPorId(id);
            return ResponseEntity.ok(new GenericResponse<>(certificado, "Certificado obtenido exitosamente", true));
        } catch (MidasExceptions e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("Error al obtener certificado: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(null, "Error interno del servidor", false));
        }
    }

    /**
     * Obtener certificados de un usuario
     */
    @GetMapping("/usuario/{usuarioId}")
    public ResponseEntity<GenericResponse<List<CertificadoDTO>>> obtenerCertificadosPorUsuario(
            @PathVariable Long usuarioId) {
        try {
            List<CertificadoDTO> certificados = certificadoService.obtenerCertificadosPorUsuario(usuarioId);
            return ResponseEntity.ok(new GenericResponse<>(certificados, "Certificados obtenidos exitosamente", true));
        } catch (Exception e) {
            log.error("Error al obtener certificados por usuario: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(null, "Error interno del servidor", false));
        }
    }

    /**
     * Obtener certificados de un curso
     */
    @GetMapping("/curso/{cursoId}")
    public ResponseEntity<GenericResponse<List<CertificadoDTO>>> obtenerCertificadosPorCurso(
            @PathVariable Long cursoId) {
        try {
            List<CertificadoDTO> certificados = certificadoService.obtenerCertificadosPorCurso(cursoId);
            return ResponseEntity.ok(new GenericResponse<>(certificados, "Certificados obtenidos exitosamente", true));
        } catch (Exception e) {
            log.error("Error al obtener certificados por curso: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(null, "Error interno del servidor", false));
        }
    }

    /**
     * Verificar si un usuario puede recibir certificado de un curso
     */
    @GetMapping("/verificar/{usuarioId}/{cursoId}")
    public ResponseEntity<GenericResponse<Map<String, Object>>> verificarElegibilidad(
            @PathVariable Long usuarioId, @PathVariable Long cursoId) {
        try {
            Map<String, Object> progreso = certificadoService.obtenerProgresoDetallado(usuarioId, cursoId);
            return ResponseEntity.ok(new GenericResponse<>(progreso, "Progreso obtenido exitosamente", true));
        } catch (Exception e) {
            log.error("Error al verificar elegibilidad: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(null, "Error interno del servidor", false));
        }
    }

    /**
     * Obtener usuarios elegibles para certificado de un curso (solo administradores)
     */
    @GetMapping("/elegibles/{cursoId}")
    public ResponseEntity<GenericResponse<List<Map<String, Object>>>> obtenerUsuariosElegibles(
            @PathVariable Long cursoId) {
        try {
            // Verificar que sea administrador
            User adminUser = obtenerUsuarioAutenticado();
            if (!esAdministrador(adminUser)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(new GenericResponse<>(null, "Solo los administradores pueden acceder a esta información", false));
            }

            List<Map<String, Object>> usuarios = certificadoService.obtenerUsuariosElegibles(cursoId);
            return ResponseEntity.ok(new GenericResponse<>(usuarios, "Usuarios elegibles obtenidos exitosamente", true));
        } catch (Exception e) {
            log.error("Error al obtener usuarios elegibles: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(null, "Error interno del servidor", false));
        }
    }

    /**
     * Obtener certificados con paginación y filtros (solo administradores)
     */
    @GetMapping
    public ResponseEntity<GenericResponse<Page<CertificadoDTO>>> obtenerCertificadosPaginados(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "fechaCreacion") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String estado) {
        try {
            // Verificar que sea administrador
            User adminUser = obtenerUsuarioAutenticado();
            if (!esAdministrador(adminUser)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(new GenericResponse<>(null, "Solo los administradores pueden acceder a esta información", false));
            }

            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                    Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            Pageable pageable = PageRequest.of(page, size, sort);
            
            Page<CertificadoDTO> certificados = certificadoService.obtenerCertificadosPaginados(search, estado, pageable);
            return ResponseEntity.ok(new GenericResponse<>(certificados, "Certificados obtenidos exitosamente", true));
        } catch (Exception e) {
            log.error("Error al obtener certificados paginados: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(null, "Error interno del servidor", false));
        }
    }

    /**
     * Obtener certificado por código
     */
    @GetMapping("/codigo/{codigo}")
    public ResponseEntity<GenericResponse<CertificadoDTO>> obtenerCertificadoPorCodigo(@PathVariable String codigo) {
        try {
            CertificadoDTO certificado = certificadoService.obtenerCertificadoPorCodigo(codigo);
            return ResponseEntity.ok(new GenericResponse<>(certificado, "Certificado obtenido exitosamente", true));
        } catch (MidasExceptions e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("Error al obtener certificado por código: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(null, "Error interno del servidor", false));
        }
    }

    /**
     * Eliminar certificado (solo administradores)
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<GenericResponse<String>> eliminarCertificado(@PathVariable Long id) {
        try {
            // Verificar que sea administrador
            User adminUser = obtenerUsuarioAutenticado();
            if (!esAdministrador(adminUser)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(new GenericResponse<>(null, "Solo los administradores pueden eliminar certificados", false));
            }

            certificadoService.eliminarCertificado(id);
            return ResponseEntity.ok(new GenericResponse<>("Certificado eliminado exitosamente", "Certificado eliminado exitosamente", true));
        } catch (MidasExceptions e) {
            return ResponseEntity.badRequest()
                    .body(new GenericResponse<>(null, e.getMessage(), false));
        } catch (Exception e) {
            log.error("Error al eliminar certificado: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(null, "Error interno del servidor", false));
        }
    }

    /**
     * Obtener el usuario autenticado
     */
    private User obtenerUsuarioAutenticado() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        return userService.findByUsername(username)
                .orElseThrow(() -> new MidasExceptions("Usuario no encontrado"));
    }

    /**
     * Verificar si el usuario es administrador
     */
    private boolean esAdministrador(User user) {
        return user.getRole() == Role.ADMIN;
    }
}
