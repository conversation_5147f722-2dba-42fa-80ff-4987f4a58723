package com.midas.crm.entity.DTO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * DTO para representar información de usuarios que han leído una notificación
 */
@Data
@NoArgsConstructor
public class NotificationReadUserDTO {

    private Long userId;
    private String userName;
    private String userEmail;
    private String nombre;
    private String apellido;
    private String sedeNombre;
    private Long sedeId;
    private LocalDateTime readAt;

    // Constructor para casos donde no tenemos fecha de lectura
    public NotificationReadUserDTO(Long userId, String userName, String userEmail) {
        this.userId = userId;
        this.userName = userName;
        this.userEmail = userEmail;
        this.readAt = LocalDateTime.now();
    }

    // Constructor completo con información adicional
    public NotificationReadUserDTO(Long userId, String userName, String userEmail,
                                   String nombre, String apellido, String sedeNombre,
                                   Long sedeId, LocalDateTime readAt) {
        this.userId = userId;
        this.userName = userName;
        this.userEmail = userEmail;
        this.nombre = nombre;
        this.apellido = apellido;
        this.sedeNombre = sedeNombre;
        this.sedeId = sedeId;
        this.readAt = readAt;
    }

    /**
     * Obtiene el nombre completo del usuario
     */
    public String getNombreCompleto() {
        if (nombre != null && apellido != null) {
            return nombre + " " + apellido;
        } else if (nombre != null) {
            return nombre;
        } else if (apellido != null) {
            return apellido;
        } else {
            return userName;
        }
    }
}
