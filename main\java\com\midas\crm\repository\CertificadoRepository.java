package com.midas.crm.repository;

import com.midas.crm.entity.Certificado;
import com.midas.crm.entity.Curso;
import com.midas.crm.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CertificadoRepository extends JpaRepository<Certificado, Long> {
    
    // Buscar certificados por usuario
    List<Certificado> findByUsuarioId(Long usuarioId);
    List<Certificado> findByUsuarioAndEstado(User usuario, String estado);
    
    // Buscar certificados por curso
    List<Certificado> findByCursoId(Long cursoId);
    List<Certificado> findByCursoAndEstado(Curso curso, String estado);
    
    // Verificar si ya existe un certificado para un usuario y curso
    boolean existsByUsuarioIdAndCursoId(Long usuarioId, Long cursoId);
    Optional<Certificado> findByUsuarioIdAndCursoId(Long usuarioId, Long cursoId);
    
    // Buscar por código de certificado
    Optional<Certificado> findByCodigoCertificado(String codigoCertificado);
    
    // Buscar certificados emitidos por un administrador
    List<Certificado> findByEmitidoPorId(Long emitidoPorId);
    
    // Buscar certificados activos
    List<Certificado> findByEstado(String estado);
    
    // Búsqueda paginada con filtros
    @Query("SELECT c FROM Certificado c " +
           "JOIN FETCH c.usuario u " +
           "JOIN FETCH c.curso cur " +
           "JOIN FETCH c.emitidoPor e " +
           "WHERE (:search IS NULL OR :search = '' OR " +
           "LOWER(u.nombre) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(u.apellido) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(cur.nombre) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(c.codigoCertificado) LIKE LOWER(CONCAT('%', :search, '%'))) " +
           "AND (:estado IS NULL OR c.estado = :estado)")
    Page<Certificado> findBySearchTermAndEstado(@Param("search") String search, 
                                               @Param("estado") String estado, 
                                               Pageable pageable);
}
