package com.midas.crm.mapper;

import com.midas.crm.entity.Certificado;
import com.midas.crm.entity.Curso;
import com.midas.crm.entity.User;
import com.midas.crm.entity.DTO.certificado.CertificadoCreateDTO;
import com.midas.crm.entity.DTO.certificado.CertificadoDTO;

public final class CertificadoMapper {

    private CertificadoMapper() {
    }

    public static Certificado toEntity(CertificadoCreateDTO dto, User usuario, Curso curso, User emitidoPor) {
        Certificado certificado = new Certificado();
        certificado.setUsuario(usuario);
        certificado.setCurso(curso);
        certificado.setFechaEmision(dto.getFechaEmision());
        certificado.setHorasCurso(dto.getHorasCurso());
        certificado.setObservaciones(dto.getObservaciones());
        certificado.setCertificadoUrl(dto.getCertificadoUrl());
        certificado.setEmitidoPor(emitidoPor);
        certificado.setEstado("A");
        return certificado;
    }

    public static CertificadoDTO toDTO(Certificado certificado) {
        CertificadoDTO dto = new CertificadoDTO();
        dto.setId(certificado.getId());
        dto.setUsuarioId(certificado.getUsuario().getId());
        dto.setUsuarioNombre(certificado.getUsuario().getNombre());
        dto.setUsuarioApellido(certificado.getUsuario().getApellido());
        dto.setUsuarioDni(certificado.getUsuario().getDni());
        dto.setUsuarioEmail(certificado.getUsuario().getEmail());
        dto.setCursoId(certificado.getCurso().getId());
        dto.setCursoNombre(certificado.getCurso().getNombre());
        dto.setCursoDescripcion(certificado.getCurso().getDescripcion());
        dto.setFechaEmision(certificado.getFechaEmision());
        dto.setHorasCurso(certificado.getHorasCurso());
        dto.setCodigoCertificado(certificado.getCodigoCertificado());
        dto.setEmitidoPorId(certificado.getEmitidoPor().getId());
        dto.setEmitidoPorNombre(certificado.getEmitidoPor().getNombre());
        dto.setEmitidoPorApellido(certificado.getEmitidoPor().getApellido());
        dto.setObservaciones(certificado.getObservaciones());
        dto.setCertificadoUrl(certificado.getCertificadoUrl());
        dto.setEstado(certificado.getEstado());
        dto.setFechaCreacion(certificado.getFechaCreacion());
        dto.setFechaActualizacion(certificado.getFechaActualizacion());
        return dto;
    }
}
