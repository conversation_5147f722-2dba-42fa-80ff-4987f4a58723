package com.midas.crm.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Entidad que representa un certificado emitido a un usuario por completar un
 * curso
 */
@Entity
@Table(name = "certificados")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Certificado {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_id", nullable = false)
    private User usuario;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "curso_id", nullable = false)
    private Curso curso;

    @Column(name = "fecha_emision", nullable = false)
    private LocalDateTime fechaEmision;

    @Column(name = "horas_curso", nullable = false)
    private Integer horasCurso; // Duración total del curso en horas

    @Column(name = "codigo_certificado", nullable = false, unique = true, length = 50)
    private String codigoCertificado; // Código único del certificado

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "emitido_por", nullable = false)
    private User emitidoPor; // Usuario administrador que emitió el certificado

    @Column(name = "observaciones", length = 500)
    private String observaciones;

    @Column(name = "certificado_url", length = 1000)
    private String certificadoUrl; // URL del certificado en Firebase

    @Column(name = "estado", nullable = false, length = 1)
    private String estado = "A"; // A: Activo, I: Inactivo

    @Column(name = "fecha_creacion", nullable = false)
    private LocalDateTime fechaCreacion;

    @Column(name = "fecha_actualizacion")
    private LocalDateTime fechaActualizacion;

    @PrePersist
    protected void onCreate() {
        fechaCreacion = LocalDateTime.now();
        if (fechaEmision == null) {
            fechaEmision = LocalDateTime.now();
        }
        if (codigoCertificado == null) {
            generarCodigoCertificado();
        }
    }

    @PreUpdate
    protected void onUpdate() {
        fechaActualizacion = LocalDateTime.now();
    }

    /**
     * Genera un código único para el certificado
     */
    private void generarCodigoCertificado() {
        // Formato: CERT-YYYYMMDD-HHMMSS-ID_USUARIO-ID_CURSO
        String timestamp = LocalDateTime.now().toString().replaceAll("[^0-9]", "").substring(0, 14);
        this.codigoCertificado = String.format("CERT-%s-%d-%d",
                timestamp,
                usuario != null ? usuario.getId() : 0,
                curso != null ? curso.getId() : 0);
    }
}
